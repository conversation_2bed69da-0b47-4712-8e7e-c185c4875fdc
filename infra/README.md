# First build of image and push to the repo
az login
az acr login --name acrodmori

# DEV
## Web
docker build -f Dockerfile-web -t odmori-web-dev:latest --platform linux/amd64 .
docker tag odmori-web-dev:latest acrodmori.azurecr.io/odmori-web-dev:latest
docker push acrodmori.azurecr.io/odmori-web-dev:latest

## API
docker build -f Dockerfile-api -t odmori-api-dev:latest --platform linux/amd64 .
docker tag odmori-api-dev:latest acrodmori.azurecr.io/odmori-api-dev:latest
docker push acrodmori.azurecr.io/odmori-api-dev:latest

# PROD
## Web
docker build -f Dockerfile-web -t odmori-web-prod:latest --platform linux/amd64 .
docker tag odmori-web-prod:latest acrodmori.azurecr.io/odmori-web-prod:latest
docker push acrodmori.azurecr.io/odmori-web-prod:latest

## API
docker build -f Dockerfile-api -t odmori-api-prod:latest --platform linux/amd64 .
docker tag odmori-api-prod:latest acrodmori.azurecr.io/odmori-api-prod:latest
docker push acrodmori.azurecr.io/odmori-api-prod:latest


## Deploy image to container app
### WEB
az containerapp update \
--name capp-odmori-web-prod \
--resource-group rg-odmori-prod \
--image acrodmori.azurecr.io/odmori-web-prod:latest

### API
az containerapp update \
--name capp-odmori-api-prod \
--resource-group rg-odmori-prod \
--image acrodmori.azurecr.io/odmori-api-prod:latest

# Deployment
## DEV

### What If
az deployment group what-if --resource-group rg-odmori-dev --template-file main.bicep --parameters main.dev.parameters.json


### Deployment
az deployment group create --resource-group rg-odmori-dev --template-file main.bicep --parameters main.dev.parameters.json


## PROD

### What If
az deployment group what-if --resource-group rg-odmori-prod --template-file main.bicep --parameters main.prod.parameters.json

# Deployment
az deployment group create --resource-group rg-odmori-prod --template-file main.bicep --parameters main.prod.parameters.json



# First Run (Create new environment)

## Manual work
- Create resource group and keyvault for given env

- Create app registration for microsoft external registration

## Required secrets
- firebase-admin-service-account (download firebase admin json file and copy content into secret)
- pgserver-admin (specify admin password for postgres - by your choice, but keep it complex)
- google-maps-key (get key from google console)


# Publicly exposed app issue
Due to chicken-egg problem, during for a first run (to create environment) 
update all publicly exposed container apps:
1. Comment managed certificate module
2. Set ingress customDomains -> bindingType to Disabled
Then deploy bicep and then change back to normal and run again
https://johnnyreilly.com/azure-container-apps-bicep-managed-certificates-custom-domains

```
     ingress: {
        external: true
        targetPort: 8080
        customDomains: [
          {
            name: domain
            bindingType: 'Disabled'
          }
        ]
      }
```

Normal
```
     ingress: {
        external: true
        targetPort: 8080
        customDomains: [
          {
            name: domain
            certificateId: managedCertificate.id
            bindingType: 'SniEnabled'
          }
        ]
      }
```

# Select resources for deploy
To deploy only specific resources, put module names inside "resources" value in main.[env].parameters.json file.
eg.
```
    "resources": {
      "value": "q360IdentityApiModule,q360CoreApiModule"
    },
```
**Leave value as empty to deploy all.**

# TODO
- add hc checks